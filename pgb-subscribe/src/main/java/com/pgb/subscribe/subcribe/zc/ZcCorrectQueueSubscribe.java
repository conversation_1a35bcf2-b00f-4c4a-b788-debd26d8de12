package com.pgb.subscribe.subcribe.zc;

import cn.hutool.core.util.NumberUtil;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.service.db.PgZcAnswerService;
import com.pgb.service.domain.GlobQueueConstants;
import com.pgb.subscribe.service.ZcCorrectSubscribeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 字词 渲染pdf 队列监听
 */
@Component("ZcCorrectQueueSubscribe")
@Slf4j
@RequiredArgsConstructor
public class ZcCorrectQueueSubscribe implements CommandLineRunner {

    private final PgZcAnswerService pgZcAnswerService;

    private final ZcCorrectSubscribeService zcCorrectSubscribeService;

    @Override
    public void run(String... args) throws Exception {

        zcCorrectSubscribeService.localCorrect(1941020339328114690L);
        // 结束程序
//          System.exit(0);
        // 云批改
//        zcCorrectSubscribeService.cloudCorrect(1945684399563849729L);

//        if (EnvUtils.isProd() || EnvUtils.isDev()){
//            // 立即执行一次
//            pgZcAnswerService.queryToCorrect();
//            // 进行题目批改
//            int processNum = NumberUtil.min(Runtime.getRuntime().availableProcessors() * 10, 80);
//            zcCorrectSubscribeService.subscribe(true, GlobQueueConstants.PGB_XCX_ZC_CORRECT_QUEUE.name(), processNum);
//        }

    }

}
