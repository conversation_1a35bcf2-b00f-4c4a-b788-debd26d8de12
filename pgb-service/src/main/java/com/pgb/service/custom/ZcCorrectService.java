package com.pgb.service.custom;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.github.difflib.DiffUtils;
import com.github.difflib.patch.DeltaType;
import com.github.difflib.patch.Patch;
import com.pgb.common.core.utils.EnvUtils;
import com.pgb.common.ocr.OCRService;
import com.pgb.common.ocr.domain.OCRChars;
import com.pgb.common.ocr.domain.OCRLocation;
import com.pgb.common.ocr.domain.OCRResult;
import com.pgb.common.ocr.domain.OCRWordResult;
import com.pgb.common.oss.service.OssService;
import com.pgb.service.custom.model.DictationCorrect;
import com.pgb.service.custom.model.EngDictationCorrect;
import com.pgb.service.custom.model.TextWritingCorrect;
import com.pgb.service.db.PgZcAnswerService;
import com.pgb.service.db.PgZcQuestionService;
import com.pgb.service.domain.common.image.FilePaperImg;
import com.pgb.service.domain.zc.common.MarkLoc;
import com.pgb.service.domain.zc.common.ZcChar;
import com.pgb.service.domain.zc.common.ZcLocation;
import com.pgb.service.domain.zc.answer.PgZcAnswer;
import com.pgb.service.domain.zc.question.area.CalcAreaForm;
import com.pgb.service.domain.zc.question.PgZcQuestion;
import com.pgb.service.domain.zc.question.area.CalcAreaResult;
import com.pgb.service.domain.zc.question.chinese.ZcSubmitForm;
import com.pgb.service.domain.zc.question.chinese.dictation.ZcDictation;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWord;
import com.pgb.service.domain.zc.question.chinese.pinyinAndWord.PinyinAndWordResult;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlank;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlankItem;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlankItem.TextBlankItemMark;
import com.pgb.service.domain.zc.question.chinese.textBlank.TextBlankItem.TextBlankMarkArea;
import com.pgb.service.domain.zc.question.english.dictation.EnDictation;
import com.pgb.service.domain.zc.question.english.translation.Translation;
import com.pgb.service.domain.zc.question.english.translation.Translation.Config;
import com.pgb.service.domain.zc.word.TextWordInfo;
import com.pgb.service.domain.zc.word.WordItem;
import com.pgb.service.domain.zc.word.english.EngWordItem;
import com.pgb.service.enums.CorrectStatusEnum;
import com.pgb.service.enums.ZcQuestionTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Component;

import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.ObjectOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * Created by 2025/4/22 12:16
 */
@Component("ZcCorrectService")
@RequiredArgsConstructor
@Slf4j
public class ZcCorrectService {

    private final PgZcAnswerService pgZcAnswerService;

    private final CorrectService correctService;

    private final PgZcQuestionService pgZcQuestionService;

    private final OCRService ocrService;

    private final EngDictationCorrect engDictationCorrect;

    private final DictationCorrect dictationCorrect;

    private final TextWritingCorrect textWritingCorrect;

    private final OssService ossService;

    public void correct(PgZcAnswer zcAnswer) {

        if (ObjectUtil.isNull(zcAnswer)) {
            log.info("无效的批改题目：{}", zcAnswer.getId());
            return;
        }

        if (!zcAnswer.getStatus().equals(CorrectStatusEnum.Uploaded)) {
            log.info("当前题目已被批改：{}", zcAnswer.getId());
            return;
        }

        // 兼容化图片
        processImg(zcAnswer);

        // 执行AI批改
        doCorrect(zcAnswer);

        pgZcAnswerService.updateById(zcAnswer);
    }

    // 开始批改题目
    public void doCorrect(PgZcAnswer zcAnswer) {

        // 开始统计批改时长
        TimeInterval timer = DateUtil.timer();
        log.info("开始批改字词题：{}", zcAnswer.getId());

        ZcSubmitForm form = JSONUtil.toBean(JSONUtil.toJsonStr(zcAnswer.getAnswer()), ZcSubmitForm.class);

        // 无效答案
        if (CollUtil.isEmpty(form.getUserImgAnswerList())) {
            zcAnswer.setAiTokens(0L);
        }
        // 执行批改
        else {

            // 看拼音写词语
            if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.PyToWord)) {
                pyAndWordCorrect(zcAnswer);
            }
            // 看词语写拼音
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.WordToPy)) {
                pyAndWordCorrect(zcAnswer);
            }
            // 课文填空
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.TextBlank)) {
                textBlankCorrect(zcAnswer);
            }
            // AI字词情景式出题
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.SceneBlank)) {
                textBlankCorrect(zcAnswer);
            }
            // AI默写情景式出题
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.TextSceneBlank)) {
                textBlankCorrect(zcAnswer);
            }
            // 词语听写
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.Dictation)) {
                dictationCorrect(zcAnswer);
            }
            // 课文默写
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.TextWriting)) {
                textWritingCorrect(zcAnswer);
            }
            // 英语听写
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.EnDictation)) {
                enDictationCorrect(zcAnswer);
            }
            // 英语中英互译
            else if (zcAnswer.getQuesType().equals(ZcQuestionTypeEnum.EnTranslation)) {
                enTranslationCorrect(zcAnswer);
            }

        }
        // 更新状态
        zcAnswer.setStatus(CorrectStatusEnum.Corrected);
        // 批改时长
        zcAnswer.setCorrectTime(new Date());
        zcAnswer.setCorrectDuration((int) timer.intervalSecond());
        pgZcAnswerService.updateById(zcAnswer);

        log.info("批改完成：{}，总用时：{}秒", zcAnswer.getId(), timer.intervalSecond());
    }

    /**
     * 【看拼音写词语】批改
     *
     * @param zcAnswer
     */
    private void pyAndWordCorrect(PgZcAnswer zcAnswer) {

        // 初始化批改结果
        PinyinAndWordResult result = new PinyinAndWordResult();

        // 用户答案
        ZcSubmitForm form = JSONUtil.toBean(JSONUtil.toJsonStr(zcAnswer.getAnswer()), ZcSubmitForm.class);

        // 获取题目json
        String questionJsonUrl = form.getQuestionJsonUrl();

        String jsonContent = HttpUtil.get(questionJsonUrl);

        PgZcQuestion question = JSONUtil.toBean(jsonContent, PgZcQuestion.class);

        // 将模板PDF转为图片
        String pdfUrl = pgZcQuestionService.getPdfUrlByType(question.getPdfUrls(), "0");

        if (ObjectUtil.isNull(pdfUrl)) {

            throw new RuntimeException("请先生成题目模板pdf！");
        }

        List<BufferedImage> templateImgList = pdf2Img(pdfUrl);

        // 计算模板图片下字词的坐标
        PinyinAndWord templateWord = calcPinyinTemplatePosition(question, templateImgList);

        // 将用户图片的字词坐标计算出来
        List<List<WordItem>> userWordList = calcUserWordPosition(form, templateWord, templateImgList, question.getUserId());

        // ocr匹配答案
        ocrAndMatch(form, userWordList);

        // 计算正误
        calcCorrect(question.getType(), userWordList);

        result.setUserWordList(userWordList);
        result.setUserImgAnswerList(form.getUserImgAnswerList());

        zcAnswer.setCorrectResult(result);
    }


    /**
     * 【课文填空】批改
     */
    public void textBlankCorrect(PgZcAnswer zcAnswer) {

        // 初始化批改结果
        PinyinAndWordResult result = new PinyinAndWordResult();

        // 用户答案
        ZcSubmitForm form = JSONUtil.toBean(JSONUtil.toJsonStr(zcAnswer.getAnswer()), ZcSubmitForm.class);

        // 获取题目json
        String questionJsonUrl = form.getQuestionJsonUrl();

        String jsonContent = HttpUtil.get(questionJsonUrl);

        PgZcQuestion question = JSONUtil.toBean(jsonContent, PgZcQuestion.class);

        // 将模板PDF转为图片
        String pdfUrl = pgZcQuestionService.getPdfUrlByType(question.getPdfUrls(), "0");
        List<BufferedImage> templateImgList = pdf2Img(pdfUrl);

        // 将坐标按比例进行缩放
        TextBlank textBlank = calcTextTemplatePosition(question, templateImgList);

        // 按用户上传图片重新计算坐标
        List<List<TextBlankItemMark>> markList = calcUserWordPosition(form, textBlank, templateImgList);

        // ocr匹配答案
        List<List<TextBlankMarkArea>> ocrAndMatch = textOcrAndMatch(form, markList);

        log.info("用户答案：{}", ocrAndMatch);

        // 判断正误
        List<List<WordItem>> userWordList = calcTextCorrect(ocrAndMatch, textBlank);

        // 批改结果
        result.setUserWordList(userWordList);
        result.setUserImgAnswerList(form.getUserImgAnswerList());

        zcAnswer.setCorrectResult(result);
    }


    /**
     * 【语文听写】批改
     */
    public void dictationCorrect(PgZcAnswer zcAnswer) {

        dictationCorrect.correct(zcAnswer);
    }

    /**
     * 【语文默写】批改
     */
    public void textWritingCorrect(PgZcAnswer zcAnswer) {

        textWritingCorrect.correct(zcAnswer);
    }

    /**
     * 【英语听写】批改
     */
    public void enDictationCorrect(PgZcAnswer zcAnswer) {

        engDictationCorrect.correct(zcAnswer);
    }


    /**
     * 【中英互译】批改
     */
    public void enTranslationCorrect(PgZcAnswer zcAnswer) {

        // 初始化批改结果
        PinyinAndWordResult result = new PinyinAndWordResult();

        // 用户答案
        ZcSubmitForm form = JSONUtil.toBean(JSONUtil.toJsonStr(zcAnswer.getAnswer()), ZcSubmitForm.class);

        // 获取题目json
        String questionJsonUrl = form.getQuestionJsonUrl();

        String jsonContent = HttpUtil.get(questionJsonUrl);

        PgZcQuestion question = JSONUtil.toBean(jsonContent, PgZcQuestion.class);

        // 将模板PDF转为图片
        String pdfUrl = pgZcQuestionService.getPdfUrlByType(question.getPdfUrls(), "0");
        List<BufferedImage> templateImgList = pdf2Img(pdfUrl);

        // 将坐标按A4比例缩放
        Translation translation = calcTransTemplatePosition(question, templateImgList);

        // 按用户上传图片重新计算坐标
        List<List<WordItem>> wordList = calcUserWordPosition(form, translation, templateImgList);

        // 匹配ocr用户答案
        transOcrAndMatch(form, wordList, translation);

        // 判断正误
        calcTransCorrect(wordList);

        // 批改结果
        result.setUserWordList(wordList);
        result.setUserImgAnswerList(form.getUserImgAnswerList());

        zcAnswer.setCorrectResult(result);
    }


    /**
     * 【中英互译】计算正误
     *
     * @param wordList
     */
    private void calcTransCorrect(List<List<WordItem>> wordList) {

        for (List<WordItem> pageWords : wordList) {
            for (WordItem wordItem : pageWords) {

                // 记录标记位置
                List<MarkLoc> markLocs = new ArrayList<>();
                MarkLoc markLoc = new MarkLoc();
                if (StrUtil.isNotBlank(wordItem.getUserContent())) {

                    // 去除用户答案和标准答案大小写的影响
                    wordItem.setWord(wordItem.getWord().toLowerCase());
                    wordItem.setUserContent(wordItem.getUserContent().toLowerCase());

                    if (StrUtil.isNotBlank(wordItem.getWord())) {
                        if (wordItem.getWord().contains(wordItem.getUserContent())) {
//                            wordItem.setRightType(1); // 正确
                            // 记录标记位置
                            markLoc.setZcLocation(wordItem.getLocation());
                            markLoc.setRightType(1);
                            markLocs.add(markLoc);
                            wordItem.setMarkLocation(markLocs);

                        } else {
//                            wordItem.setRightType(2); // 错误
                            markLoc.setZcLocation(wordItem.getLocation());
                            markLoc.setRightType(2);
                            markLoc.setErrorWord(wordItem.getWord());
                            markLocs.add(markLoc);
                            wordItem.setMarkLocation(markLocs);
                        }
                    }
                } else {
//                    wordItem.setRightType(2); // 错误
                    markLoc.setZcLocation(wordItem.getLocation());
                    markLoc.setRightType(2);
                    markLoc.setErrorWord(wordItem.getWord());
                    markLocs.add(markLoc);
                    wordItem.setMarkLocation(markLocs);
                }
            }
        }
    }


    /**
     * 【中英互译】匹配OCR用户答案
     *
     * @param form
     * @param wordList
     * @param translation
     */
    private void transOcrAndMatch(ZcSubmitForm form, List<List<WordItem>> wordList, Translation translation) {
        // OCR识别用户图片中的所有字符
        List<OCRResult> ocrResults = new ArrayList<>();
        for (FilePaperImg img : form.getUserImgAnswerList()) {
            OCRResult result = ocrService.handWriting(img.getImgUrl());
            ocrResults.add(result);
        }

        // 获取题目配置中的Translation对象
        Config config = translation.getConfig();
        // 是否显示英文单词
        boolean showEng = ObjectUtil.isNotNull(translation.getConfig().getShowEng()) && config.getShowEng();
        // 是否显示中文释义
        boolean showChinese = ObjectUtil.isNotNull(config.getShowCh()) && config.getShowCh();

        // 遍历每一页的WordItem
        for (int pageIndex = 0; pageIndex < wordList.size(); pageIndex++) {
            List<WordItem> pageWords = wordList.get(pageIndex);

            if (pageIndex >= ocrResults.size()) continue;

            OCRResult ocrResult = ocrResults.get(pageIndex);
            List<OCRChars> charList = ocrResult.getWords_result().stream()
                    .flatMap(word -> word.getChars().stream())
                    .toList();
            if (CollUtil.isEmpty(charList)) {
                continue;
            }

            // 遍历当前页的单词
            Iterator<WordItem> iterator = pageWords.iterator();
            while (iterator.hasNext()) {
                WordItem wordItem = iterator.next();
                // 标准答案
                String standardWord = wordItem.getWord();
                // 标准单词坐标
                ZcLocation location = wordItem.getLocation();

                // 判断是英文还是中文
                boolean isEnglish = standardWord.matches("^[a-zA-Z\\s\\-,()0-9]+$");

                // 如果是英文，但只显示中文，则跳过
                if (showEng && isEnglish) {
                    iterator.remove();
                    continue;
                }
                // 如果是中文，但只显示英文，则跳过
                if (showChinese && !isEnglish) {
                    iterator.remove();
                    continue;
                }

                // 构建标准答案区域
                Rectangle2D.Float questionRect = new Rectangle2D.Float(
                        location.getLeft(),
                        location.getTop(),
                        location.getWidth(),
                        location.getHeight());

                // 匹配OCR识别结果
                for (OCRChars chars : charList) {
                    // 获取ocr识别的坐标
                    OCRLocation charLoc = chars.getLocation();

                    Rectangle2D.Float charRect = new Rectangle2D.Float(
                            charLoc.getLeft(), charLoc.getTop(),
                            charLoc.getWidth(), charLoc.getHeight());

                    // 判断是否有交集
                    if (questionRect.intersects(charRect)) {
                        // 设置用户作答内容
                        if (StrUtil.isBlank(wordItem.getUserContent())) {
                            wordItem.setUserContent(chars.getChars());
                        } else {
                            wordItem.setUserContent(wordItem.getUserContent() + chars.getChars());
                        }
                        // 设置用户作答内容整体坐标
//                        wordItem.getUserTextLocationList().add(
//                                new ZcLocation(
//                                        charLoc.getLeft(),
//                                        charLoc.getTop(),
//                                        charLoc.getWidth(),
//                                        charLoc.getHeight(),
//                                        pageIndex
//                                )
//                        );
                    }
                }
            }
        }
    }


    /**
     * 【中英互译】按用户上传图片重新计算坐标
     *
     * @param form
     * @param translation
     * @param templateImgList
     * @return
     */
    private List<List<WordItem>> calcUserWordPosition(ZcSubmitForm form, Translation translation, List<BufferedImage> templateImgList) {

        // 用户图片
        List<FilePaperImg> userImgList = form.getUserImgAnswerList();


        // 全部单词列表 存储每页的
        List<List<WordItem>> wordListByPage = new ArrayList<>();

        // 获取模板中的英文单词列表
        List<EngWordItem> enWordList = translation.getEngWordList();

        // 遍历用户上传图片
        for (int i = 0; i < Math.min(userImgList.size(), templateImgList.size()); i++) {

            // 初始化区域计算请求参数
            List<CalcAreaForm> forms = new ArrayList<>();

            // 获取用户上传图片
            String userImgUrl = userImgList.get(i).getImgUrl();
            String userImgBase64 = ImgUtil.toBase64(
                    ImgUtil.getImage(URLUtil.url(userImgUrl)), ImgUtil.IMAGE_TYPE_PNG
            );

            // 获取pdf转的图片
            BufferedImage pdfImage = templateImgList.get(i);
            String pdfBase64 = ImgUtil.toBase64(pdfImage, ImgUtil.IMAGE_TYPE_PNG);

            CalcAreaForm areaForm = new CalcAreaForm();
            areaForm.setUserImg(userImgBase64);
            areaForm.setTemplateImg(pdfBase64);

            // 初始化所有坐标列表
            List<ZcLocation> locationList = new ArrayList<>();
            // 当页的单词
            List<WordItem> wordItemsInCurrentPage = new ArrayList<>();

            // 遍历单词提取坐标
            if (CollUtil.isNotEmpty(enWordList)) {

                for (EngWordItem wordItem : enWordList) {

                    // 是否显示中文
                    Boolean showCh = translation.getConfig().getShowCh();
                    // 是否显示英文
                    Boolean showEng = translation.getConfig().getShowEng();

                    // 如果显示英文且不显示中文 或者都显示 ->拿中文的坐标
                    if (showEng) {
                        // 跳过空白内容
                        if (ObjectUtil.isNull(wordItem.getChineseInfo().getLocation())) {
                            continue;
                        }

                        // 中文释义的坐标
                        if (ObjectUtil.isNotNull(wordItem.getChineseInfo())) {
                            // 获取坐标
                            ZcChar chineseInfo = wordItem.getChineseInfo();
                            ZcLocation chineseLoc = chineseInfo.getLocation();

                            if (chineseLoc.getPageNo().equals(i)) {
                                locationList.add(chineseLoc);

                                WordItem item = new WordItem();
                                item.setWord(chineseInfo.getChars());
                                item.setLocation(chineseLoc);
                                wordItemsInCurrentPage.add(item);
                            }
                        }
                    }
                    // 拿英文坐标
                    else {
                        // 跳过空白内容
                        if (ObjectUtil.isNull(wordItem.getWordInfo().getLocation())) {
                            continue;
                        }

                        // 单词坐标
                        ZcChar wordInfo = wordItem.getWordInfo();
                        ZcLocation wordLoc = wordInfo.getLocation();

                        // 按页面添加
                        if (wordLoc.getPageNo().equals(i)) {
                            locationList.add(wordLoc);

                            // 构建WordItem对象
                            WordItem item = new WordItem();
                            item.setWord(wordItem.getWord());
                            item.setLocation(wordLoc);
                            wordItemsInCurrentPage.add(item);
                        }
                    }
                }
            }

            areaForm.setLocationList(locationList);
            forms.add(areaForm);
            wordListByPage.add(wordItemsInCurrentPage);

//            // 使用JSON序列化计算
//            byte[] jsonBytes = JSONUtil.toJsonStr(forms).getBytes(StandardCharsets.UTF_8);
//            int jsonSize = jsonBytes.length; // 获取字节数
//
//            log.info("总大小：{}", jsonSize);

            // 坐标计算
            List<CalcAreaResult> results = calculateArea(forms);

            // 更新坐标
            for (int j = 0; j < results.size(); j++) {
                List<ZcLocation> userLocations = results.get(j).getUserLocationList();
                List<WordItem> pageWords = wordListByPage.get(i);

                // 将计算出的坐标更新到对应的 WordItem 上
                for (int k = 0; k < Math.min(userLocations.size(), pageWords.size()); k++) {
                    pageWords.get(k).setLocation(userLocations.get(k));
                }
            }
        }
        return wordListByPage;
    }


    /**
     * 【中英互译】模板位置信息计算
     *
     * @param question
     * @param templateImgList
     * @return
     */
    private Translation calcTransTemplatePosition(PgZcQuestion question, List<BufferedImage> templateImgList) {

        Translation translation = JSONUtil.toBean(JSONUtil.toJsonStr(question.getContentJson()), Translation.class);
        // 获取模板的整体坐标
        ZcLocation location = translation.getLocation();

        // 获取英文单词列表
        List<EngWordItem> engWordList = translation.getEngWordList();

        if (CollUtil.isNotEmpty(engWordList)) {
            // 遍历英语单词调整位置
            for (EngWordItem wordItem : engWordList) {

                // 跳过空白内容
                if (ObjectUtil.isNull(wordItem.getWordInfo().getLocation()) && ObjectUtil.isNull(wordItem.getChineseInfo().getLocation())) {
                    continue;
                }

                // 获取单词
                ZcChar wordInfo = wordItem.getWordInfo();
                // 获取中文释义
                ZcChar chineseInfo = wordItem.getChineseInfo();

                // 英语单词坐标信息
                if (ObjectUtil.isNotNull(wordInfo.getLocation())) {
                    ZcLocation wordLoc = wordInfo.getLocation();
                    // 页码
                    int pageNo = wordLoc.getPageNo();
                    if (pageNo >= templateImgList.size() || pageNo < 0) {
                        continue;
                    }
                    // 根据页码获取图片
                    BufferedImage image = templateImgList.get(pageNo);
                    // 计算缩放比例
                    float scaleX = image.getWidth() / location.getWidth();
                    float scaleY = image.getHeight() / location.getHeight();

                    // 应用缩放
                    wordLoc.setLeft(wordLoc.getLeft() * scaleX);
                    wordLoc.setTop(wordLoc.getTop() * scaleY);
                    wordLoc.setWidth(wordLoc.getWidth() * scaleX);
                    wordLoc.setHeight(wordLoc.getHeight() * scaleY);
                }
                // 中文释义坐标信息
                else if (ObjectUtil.isNotNull(chineseInfo.getLocation())) {

                    if (ObjectUtil.isNotNull(chineseInfo.getLocation())) {
                        ZcLocation chineseLoc = chineseInfo.getLocation();

                        // 页码
                        int pageNo = chineseLoc.getPageNo();
                        if (pageNo >= templateImgList.size() || pageNo < 0) {
                            continue;
                        }
                        // 根据页码获取图片
                        BufferedImage image = templateImgList.get(pageNo);
                        // 计算缩放比例
                        float scaleX = image.getWidth() / location.getWidth();
                        float scaleY = image.getHeight() / location.getHeight();

                        // 应用缩放
                        chineseLoc.setLeft(chineseLoc.getLeft() * scaleX);
                        chineseLoc.setTop(chineseLoc.getTop() * scaleY);
                        chineseLoc.setWidth(chineseLoc.getWidth() * scaleX);
                        chineseLoc.setHeight(chineseLoc.getHeight() * scaleY);
                    }
                }
            }
        }

        return translation;
    }

    /**
     * 【课文填空】OCR 根据位置信息匹配用户作答内容
     *
     * @param form
     * @param userMarksList
     */
    private List<List<TextBlankMarkArea>> textOcrAndMatch(ZcSubmitForm form, List<List<TextBlankItemMark>> userMarksList) {

        //  横线（合并输入框）、（田字格、括号）
        List<List<TextBlankMarkArea>> textMarkAreaList = new ArrayList<>();
        for (List<TextBlankItemMark> text : userMarksList) {
            // 把text 根据start进行排序，从小到大
            text.sort(Comparator.comparingInt(TextBlankItemMark::getStart));

            //连续 start和 end合并为一个
            List<TextBlankMarkArea> markAreaList = new ArrayList<>();
            TextBlankMarkArea markArea = new TextBlankMarkArea();

            for (TextBlankItemMark mark : text) {
                // 第一次加内容的时候
                if (ObjectUtil.isNull(markArea.getStart())) {
                    markArea.setStart(mark.getStart());
                    markArea.setEnd(mark.getEnd());
                    if (StrUtil.isNotBlank(mark.getText())) {
                        markArea.getText().add(mark.getText());
                    }
                    if (StrUtil.isNotBlank(mark.getPinyin())) {
                        markArea.getPinyin().add(mark.getPinyin());
                    }

                    markArea.setLocationList(new ArrayList<>(List.of(mark.getLocation())));
                }
                // 如果是分页，则强制换
                else if (Objects.equals(mark.getStart(), markArea.getEnd())
                        && Objects.equals(markArea.getLocation().getPageNo(), mark.getLocation().getPageNo())
                ) {
                    // 可以合并
                    markArea.setEnd(mark.getEnd());
                    if (StrUtil.isNotBlank(mark.getText())) {
                        markArea.getText().add(mark.getText());
                    }
                    if (StrUtil.isNotBlank(mark.getPinyin())) {
                        markArea.getPinyin().add(mark.getPinyin());
                    }
                    if (ObjectUtil.isNotNull(mark.getLocation())) {
                        markArea.getLocationList().add(mark.getLocation());
                    }
                } else {
                    // 深克隆
                    TextBlankMarkArea cloned = JSONUtil.toBean(JSONUtil.toJsonStr(markArea), TextBlankMarkArea.class);
                    markAreaList.add(cloned);

                    // 清空旧数据，重新初始化新 markArea
                    markArea = new TextBlankMarkArea();
                    markArea.setStart(mark.getStart());
                    markArea.setEnd(mark.getEnd());

                    if (StrUtil.isNotBlank(mark.getText())) {
                        markArea.getText().add(mark.getText());
                    }
                    if (StrUtil.isNotBlank(mark.getPinyin())) {
                        markArea.getPinyin().add(mark.getPinyin());
                    }

                    markArea.setLocationList(new ArrayList<>(List.of(mark.getLocation())));
                }
            }

            // 加最后一个
            if (CollUtil.isNotEmpty(markArea.getText())) {

                TextBlankMarkArea cloned = JSONUtil.toBean(JSONUtil.toJsonStr(markArea), TextBlankMarkArea.class);
                markAreaList.add(cloned);
            }

            textMarkAreaList.add(markAreaList);
        }

        // 对用户图片进行ocr操作
        List<OCRResult> ocrResults = new ArrayList<>();
        for (FilePaperImg img : form.getUserImgAnswerList()) {
            OCRResult result = ocrService.handWriting(img.getImgUrl());
            ocrResults.add(result);
        }

        // 开始加学生书写内容
        for (List<TextBlankMarkArea> markAreaList : textMarkAreaList) {

            for (TextBlankMarkArea markArea : markAreaList) {

                // 如果没有 跳过
                if (ObjectUtil.isNull(markArea) || CollUtil.isEmpty(markArea.getText())) {
                    continue;
                }

                // 对应页面的ocr信息
                OCRResult ocrResult = ocrResults.get(
                        markArea.getLocation().getPageNo()
                );

                // 找所属区域的文字
                for (OCRWordResult wordResult : ocrResult.getWords_result()) {
                    List<OCRChars> chars = wordResult.getChars();
                    if (CollUtil.isEmpty(chars)) {
                        continue;
                    }
                    for (OCRChars charItem : chars) {
                        OCRLocation location = charItem.getLocation();
                        if (markArea.isInArea(location.getLeft(), location.getTop(), location.getWidth(), location.getHeight())) {

                            markArea.getUserText().add(charItem.getChars());

                            // 页码就是list的数量
                            markArea.getUserTextLocationList().add(
                                    new ZcLocation(
                                            location.getLeft(),
                                            location.getTop(),
                                            location.getWidth(),
                                            location.getHeight(),
                                            markArea.getLocation().getPageNo())
                            );
                        }
                    }

                }
            }
        }

        return textMarkAreaList;
    }


    /**
     * 【课文填空】计算正误
     *
     * @param textMarkAreaList
     */
    private List<List<WordItem>> calcTextCorrect(List<List<TextBlankMarkArea>> textMarkAreaList, TextBlank textBlank) {

        // 按页码分组
        Map<Integer, List<TextBlankMarkArea>> markAreasByPage = new HashMap<>();

        for (List<TextBlankMarkArea> markAreaList : textMarkAreaList) {
            for (TextBlankMarkArea markArea : markAreaList) {
                int pageNo = markArea.getLocation().getPageNo();

                // 如果当前 pageNo 的 key 不存在，则初始化空列表
                markAreasByPage.computeIfAbsent(pageNo, k -> new ArrayList<>()).add(markArea);
            }
        }

        // 创建最终的 userWordList
        List<List<WordItem>> userWordList = new ArrayList<>();

        // 确保按页码顺序排列
        List<Integer> sortPages = new ArrayList<>(markAreasByPage.keySet());
        Collections.sort(sortPages);

        // 遍历每一页的内容
        for (int pageNo : sortPages) {
            List<TextBlankMarkArea> markAreas = markAreasByPage.get(pageNo);

            List<WordItem> wordItems = new ArrayList<>();
            for (TextBlankMarkArea markArea : markAreas) {

                if (ObjectUtil.isNull(markArea) || CollUtil.isEmpty(markArea.getText())) {
                    continue;
                }

                // 创建一个WordItem来表示整个填空区域
                WordItem wordItem = new WordItem();

                // 设置标准答案（将所有文字连接起来）
                String standardAnswer = String.join("", markArea.getText());
                wordItem.setWord(standardAnswer);

                // 设置用户答案（将所有用户输入文字连接起来）
                String userAnswerOriginal = String.join("", markArea.getUserText());
                wordItem.setUserContent(userAnswerOriginal);

                // 设置位置信息（使用整个区域的位置）
                wordItem.setLocation(markArea.getLocation());

                // 设置用户文字坐标列表
                wordItem.setUserTextLocationList(markArea.getUserTextLocationList());

                // 清洗标准答案和用户答案，去除标点符号
                String cleanedStandardAnswer = cleanText(standardAnswer);
                String cleanedUserAnswer = cleanText(userAnswerOriginal);

                // 创建用户原始文本到清洗后文本的位置映射
                List<Integer> userOriginalToCleanedMapping = createPositionMapping(userAnswerOriginal, cleanedUserAnswer);

                // 逐字符对比，标注错别字位置
                List<MarkLoc> markLocations = new ArrayList<>();

                // 将清洗后的标准答案和用户答案转为字符数组
                char[] standardChars = cleanedStandardAnswer.toCharArray();
                char[] userChars = cleanedUserAnswer.toCharArray();

                // 获取题目样式类型（0：划线样式，其他：非划线样式）
                Integer hideType = textBlank.getConfig().getHideType();
                boolean isLineStyle = (hideType != null && hideType == 0);

                // 按标准答案的长度进行对比
                for (int i = 0; i < standardChars.length; i++) {
                    char standardChar = standardChars[i];
                    char userChar = i < userChars.length ? userChars[i] : '\0';

                    // 如果字符不匹配，创建错误标注
                    if (standardChar != userChar) {
                        MarkLoc markLoc = new MarkLoc();

                        // 根据样式类型设置坐标
                        if (isLineStyle) {
                            // 划线样式：如果用户作答不为空并且为错误时，使用用户作答坐标；如果为空使用标准答案坐标
                            if (userChar != '\0') {
                                // 用户有作答，使用用户作答的坐标
                                // 通过位置映射找到用户原始文本中对应的位置
                                if (i < userOriginalToCleanedMapping.size()) {
                                    int originalIndex = userOriginalToCleanedMapping.get(i);
                                    if (originalIndex < markArea.getUserTextLocationList().size()) {
                                        markLoc.setZcLocation(markArea.getUserTextLocationList().get(originalIndex));
                                    } else {
                                        markLoc.setZcLocation(markArea.getLocation());
                                    }
                                } else {
                                    markLoc.setZcLocation(markArea.getLocation());
                                }
                            } else {
                                // 用户未作答，使用标准答案坐标
                                if (i < markArea.getLocationList().size()) {
                                    markLoc.setZcLocation(markArea.getLocationList().get(i));
                                } else {
                                    markLoc.setZcLocation(markArea.getLocation());
                                }
                            }
                        } else {
                            // 非划线样式：都使用标准答案的位置坐标
                            if (i < markArea.getLocationList().size()) {
                                markLoc.setZcLocation(markArea.getLocationList().get(i));
                            } else {
                                markLoc.setZcLocation(markArea.getLocation());
                            }
                        }

                        markLoc.setRightType(2); // 错误
                        markLoc.setErrorWord(String.valueOf(standardChar)); // 标准答案字符
                        markLocations.add(markLoc);
                    }
                }

                // 如果用户答案比标准答案长，标注多余的字符为错误
                if (userChars.length > standardChars.length) {
                    for (int i = standardChars.length; i < userChars.length; i++) {
                        MarkLoc markLoc = new MarkLoc();

                        // 根据样式类型设置坐标
                        if (isLineStyle) {
                            // 划线样式：使用用户字符的坐标
                            if (i < markArea.getUserTextLocationList().size()) {
                                markLoc.setZcLocation(markArea.getUserTextLocationList().get(i));
                            } else {
                                markLoc.setZcLocation(markArea.getLocation());
                            }
                        } else {
                            // 非划线样式：使用整个区域坐标（因为标准答案没有对应位置）
                            markLoc.setZcLocation(markArea.getLocation());
                        }

                        markLoc.setRightType(2); // 错误
                        markLoc.setErrorWord(""); // 多余字符，标准答案为空
                        markLocations.add(markLoc);
                    }
                }

                // 如果完全正确，添加一个正确的标注
                if (markLocations.isEmpty()) {
                    MarkLoc markLoc = new MarkLoc();
                    markLoc.setZcLocation(markArea.getLocation());
                    markLoc.setRightType(1); // 正确
                    markLocations.add(markLoc);
                }

                wordItem.setMarkLocation(markLocations);
                wordItems.add(wordItem);
            }

            userWordList.add(wordItems);
        }

        return userWordList;

    }


    /**
     * 【课文填空】计算模板图片坐标
     *
     * @param question
     * @param templateImgList
     */
    private TextBlank calcTextTemplatePosition(PgZcQuestion question, List<BufferedImage> templateImgList) {

        TextBlank textBlank = JSONUtil.toBean(JSONUtil.toJsonStr(question.getContentJson()), TextBlank.class);
        // 获取模板的整体宽高
        ZcLocation textLocation = textBlank.getLocation();

        // 遍历课文内容
        List<TextBlankItem> textList = textBlank.getContent();

        if (CollUtil.isNotEmpty(textList)) {
            for (TextBlankItem textItem : textList) {

                // 处理标题划线区域
                if (CollUtil.isNotEmpty(textItem.getTitleMark())) {
                    for (TextBlankItem.TextBlankItemMark titleMark : textItem.getTitleMark()) {

                        // 标题划线坐标
                        ZcLocation location = titleMark.getLocation();
                        // 页码
                        int pageNo = location.getPageNo();

                        if (pageNo >= templateImgList.size() || pageNo < 0) {
                            continue;
                        }
                        // 根据页码获取图片
                        BufferedImage image = templateImgList.get(pageNo);

                        // 计算缩放比例
                        float scaleX = image.getWidth() / textLocation.getWidth();
                        float scaleY = image.getHeight() / textLocation.getHeight();

                        // 进行缩放
                        location.setLeft(location.getLeft() * scaleX);
                        location.setTop(location.getTop() * scaleY);
                        location.setWidth(location.getWidth() * scaleX);
                        location.setHeight(location.getHeight() * scaleY);
                    }
                }

                // 处理正文划线区域
                if (CollUtil.isNotEmpty(textItem.getTextMark())) {
                    for (TextBlankItem.TextBlankItemMark mark : textItem.getTextMark()) {
                        ZcLocation location = mark.getLocation();
                        int pageNo = location.getPageNo();

                        // 确保page在templateImgList的数量范围内 ，否则跳过
                        if (pageNo >= templateImgList.size() || pageNo < 0) {
                            continue;
                        }

                        // 根据页码找学生上传图片
                        BufferedImage image = templateImgList.get(pageNo);

                        // 如果对应页码存在 则更新坐标数据
                        float scaleX = image.getWidth() / textLocation.getWidth();
                        float scaleY = image.getHeight() / textLocation.getHeight();

                        // 应用缩放
                        location.setLeft(location.getLeft() * scaleX);
                        location.setTop(location.getTop() * scaleY);
                        location.setWidth(location.getWidth() * scaleX);
                        location.setHeight(location.getHeight() * scaleY);
                    }
                }
            }
        }
        return textBlank;
    }


    /**
     * 【课文填空】计算用户答案划线区域的坐标（按课文分组）
     *
     * @param form
     * @param templateTextBlank
     * @param templateImgList
     * @return
     */
    private List<List<TextBlankItemMark>> calcUserWordPosition(ZcSubmitForm form,
                                                               TextBlank templateTextBlank,
                                                               List<BufferedImage> templateImgList) {
        // 用户图片
        List<FilePaperImg> userImgList = form.getUserImgAnswerList();

        // 初始化区域计算请求参数
        List<CalcAreaForm> forms = new ArrayList<>();

        // 按课文存储划线区域的标记

        // 按页面存储划线区域（用于接收 OCR 匹配结果）
        List<List<TextBlankItemMark>> marksByPage = new ArrayList<>();

        // 每篇课文单独保存（保留原始结构）
        Map<TextBlankItem, List<TextBlankItemMark>> lessonToMarksMap = new LinkedHashMap<>();
        for (TextBlankItem item : templateTextBlank.getContent()) {

            lessonToMarksMap.put(item, new ArrayList<>());
        }

        for (int i = 0; i < Math.min(userImgList.size(), templateImgList.size()); i++) {

            // 获取当前页的图片
            String userImgUrl = userImgList.get(i).getImgUrl();
            String userImgBase64 = ImgUtil.toBase64(ImgUtil.getImage(URLUtil.url(userImgUrl)), ImgUtil.IMAGE_TYPE_PNG);

            BufferedImage pdfImage = templateImgList.get(i);
            String pdfBase64 = ImgUtil.toBase64(pdfImage, ImgUtil.IMAGE_TYPE_PNG);

            CalcAreaForm areaForm = new CalcAreaForm();
            areaForm.setUserImg(userImgBase64);
            areaForm.setTemplateImg(pdfBase64);

            List<ZcLocation> locations = new ArrayList<>();
            List<TextBlankItemMark> marksInCurrentPage = new ArrayList<>();

            for (TextBlankItem item : templateTextBlank.getContent()) {

                // 提取标题划线、正文划线...
                if (CollUtil.isNotEmpty(item.getTitleMark())) {
                    for (TextBlankItem.TextBlankItemMark mark : item.getTitleMark()) {
                        ZcLocation loc = mark.getLocation();
                        if (loc.getPageNo() == i) {
                            locations.add(loc);
                            marksInCurrentPage.add(mark);
                        }
                    }
                }

                if (CollUtil.isNotEmpty(item.getTextMark())) {
                    for (TextBlankItem.TextBlankItemMark mark : item.getTextMark()) {
                        ZcLocation loc = mark.getLocation();
                        if (loc.getPageNo() == i) {
                            locations.add(loc);
                            marksInCurrentPage.add(mark);
                        }
                    }
                }
            }

            areaForm.setLocationList(locations);
            forms.add(areaForm);

            // 每页保存一份（用于后续 OCR 匹配后更新坐标）
            marksByPage.add(marksInCurrentPage);


            // 遍历所有课文 item，收集当前页的 mark
            for (Map.Entry<TextBlankItem, List<TextBlankItemMark>> entry : lessonToMarksMap.entrySet()) {
                TextBlankItem item = entry.getKey();
                List<TextBlankItemMark> currentMarks = entry.getValue();

                // 标题划线区域
                if (CollUtil.isNotEmpty(item.getTitleMark())) {
                    for (TextBlankItem.TextBlankItemMark mark : item.getTitleMark()) {
                        ZcLocation loc = mark.getLocation();
//                        currentMarks.add(mark);
                        if (loc.getPageNo() == i) {
                            currentMarks.add(mark);
                        }
                    }
                }

                // 正文划线区域
                if (CollUtil.isNotEmpty(item.getTextMark())) {
                    for (TextBlankItem.TextBlankItemMark mark : item.getTextMark()) {
                        ZcLocation loc = mark.getLocation();
//                        currentMarks.add(mark);
                        if (loc.getPageNo() == i) {
                            currentMarks.add(mark);
                        }
                    }
                }

            }
        }
        // 最后统一收集成最终结果
        List<List<TextBlankItemMark>> marksByLesson = new ArrayList<>(lessonToMarksMap.values());

        // 区域计算
        List<CalcAreaResult> results = calculateArea(forms);


        // 更新用户划线区域的坐标（基于每页结构）
        for (int i = 0; i < results.size(); i++) {
            List<ZcLocation> userLocations = results.get(i).getUserLocationList();
            List<TextBlankItemMark> pageMarks = marksByPage.get(i);

            for (int j = 0; j < userLocations.size() && j < pageMarks.size(); j++) {
                pageMarks.get(j).setLocation(userLocations.get(j));
            }
        }

        return marksByLesson;
    }


    /**
     * 图片兼容化
     *
     * @param zcAnswer
     */
    public void processImg(PgZcAnswer zcAnswer) {

        ZcSubmitForm userAnswer = JSONUtil.toBean(zcAnswer.getAnswer().toString(), ZcSubmitForm.class);

        // 最多留9张
        if (userAnswer.getUserImgAnswerList().size() >= 10) {
            userAnswer.setUserImgAnswerList(
                    CollUtil.sub(userAnswer.getUserImgAnswerList(), 0, 9)
            );
        }

        // 图片兼容化处理
        for (int i = 0; i < userAnswer.getUserImgAnswerList().size(); i++) {

            // 兼容 tmp 情况
            FilePaperImg img = userAnswer.getUserImgAnswerList().get(i);

            // 兼容图片处理
            correctService.processImgV2(img);
        }
        zcAnswer.setAnswer(userAnswer);

        pgZcAnswerService.updateById(zcAnswer);
    }


    /**
     * pdf转图片
     *
     * @param pdfUrl
     * @return
     */
    private List<BufferedImage> pdf2Img(String pdfUrl) {

        // pdf转的图片
        List<BufferedImage> pdfImages = new ArrayList<>();

        // 下载 PDF 文件
        File pdf = FileUtil.createTempFile(".pdf", true);
        HttpUtil.downloadFile(pdfUrl, pdf);
        PDDocument doc;

        try {
            doc = Loader.loadPDF(FileUtil.readBytes(pdf));

            PDFRenderer renderer = new PDFRenderer(doc);

            for (int i = 0; i < doc.getNumberOfPages(); i++) {
                // 生成 PDF 截取的图片，dpi越高，越大
                BufferedImage image = renderer.renderImageWithDPI(i, 200);
                pdfImages.add(image);
            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return pdfImages;
    }


    /**
     * 【看拼音写词语】计算模板图片坐标
     *
     * @param question
     * @param templateImgList
     */
    private PinyinAndWord calcPinyinTemplatePosition(PgZcQuestion question, List<BufferedImage> templateImgList) {

        // 获取字词题目内容
        PinyinAndWord pinyinAndWord = JSONUtil.toBean(JSONUtil.toJsonStr(question.getContentJson()), PinyinAndWord.class);

        // 遍历课文内容
        List<TextWordInfo> infoList = pinyinAndWord.getContent();

        // 按课文遍历
        for (TextWordInfo info : infoList) {

            // 遍历字词列表
            for (WordItem word : info.getWordList()) {

                // 字词坐标
                ZcLocation wordLocation = word.getLocation();
                // 页数
                int pageNo = wordLocation.getPageNo();

                if (pageNo >= templateImgList.size() || pageNo < 0) {
                    continue;
                }

                // 获取图片
                BufferedImage image = templateImgList.get(pageNo);

                // 计算缩放比例
                float scaleX = image.getWidth() / pinyinAndWord.getLocation().getWidth();
                float scaleY = image.getHeight() / pinyinAndWord.getLocation().getHeight();

                // 应用缩放
                wordLocation.setLeft(wordLocation.getLeft() * scaleX);
                wordLocation.setTop(wordLocation.getTop() * scaleY);
                wordLocation.setWidth(wordLocation.getWidth() * scaleX);
                wordLocation.setHeight(wordLocation.getHeight() * scaleY);
            }
        }

        return pinyinAndWord;
    }


    /**
     * 【拼音和词语】计算用户答案划线区域的坐标
     *
     * @param form
     * @param templateWord
     * @param templateImgList
     * @return
     */
    private List<List<WordItem>> calcUserWordPosition(ZcSubmitForm form, PinyinAndWord templateWord, List<BufferedImage> templateImgList, Long userId) {
        // 用户图片
        List<FilePaperImg> userImgList = form.getUserImgAnswerList();
        // 区域计算请求参数
        List<CalcAreaForm> forms = new ArrayList<>();
        // 全部的字词列表
        List<List<WordItem>> wordList = new ArrayList<>();

        // 组装数据
        for (int i = 0; i < Math.min(userImgList.size(), templateImgList.size()); i++) {

            List<WordItem> wordItemList = new ArrayList<>();

            // 获取用户上传图片
            String userImgUrl = userImgList.get(i).getImgUrl();

            // 判断是否是扫描件
            Boolean isScan = correctService.isScan(userImgUrl);

            log.info("是否是扫描件：{}", isScan);
            String userImgBase64;

            // 如果是扫描件
            if (isScan) {
                userImgBase64 = ImgUtil.toBase64(
                        ImgUtil.getImage(URLUtil.url(userImgUrl)), ImgUtil.IMAGE_TYPE_PNG
                );
            } else {
                // 矫正用户上传图片
                BufferedImage userImgBuffer = correctService.correctImg(userImgUrl);
                userImgBase64 = ImgUtil.toBase64(userImgBuffer, ImgUtil.IMAGE_TYPE_PNG);

                // 替换用户原本图片
                String newImgUrl = replaceUserImg(userImgBuffer, userImgUrl, userId);

                userImgList.get(i).setImgUrl(newImgUrl);
                log.info("用户上传的图片：{} <--> 替换用户上传的图片：{}", userImgUrl, newImgUrl);
            }

            // 获取pdf转的图片
            BufferedImage pdfImage = templateImgList.get(i);

            String pdfBase64 = ImgUtil.toBase64(pdfImage, ImgUtil.IMAGE_TYPE_PNG);

            CalcAreaForm item = new CalcAreaForm();
            item.setUserImg(userImgBase64);
            item.setTemplateImg(pdfBase64);

            // 按当前页设置坐标
            List<ZcLocation> locationList = new ArrayList<>();
            for (TextWordInfo textWordInfo : templateWord.getContent()) {

                for (WordItem wordItem : textWordInfo.getWordList()) {
                    ZcLocation location = wordItem.getLocation();
                    if (location.getPageNo() == i) {
                        location.setTextNo(textWordInfo.getLesson());
                        locationList.add(location);
                        wordItemList.add(wordItem);
                    }
                }
            }

            item.setLocationList(locationList);
            forms.add(item);
            wordList.add(wordItemList);
        }

        // 区域计算
        List<CalcAreaResult> calcAreaResults = calculateArea(forms);

        for (int i = 0; i < calcAreaResults.size(); i++) {

            List<ZcLocation> userLocations = calcAreaResults.get(i).getUserLocationList();

            for (int i1 = 0; i1 < userLocations.size(); i1++) {

                // 记录用户作答位置
                wordList.get(i).get(i1).setLocation(
                        userLocations.get(i1)
                );
            }
        }

        return wordList;
    }


    /**
     * 【拼音和词语】OCR位置信息匹配
     *
     * @param form
     * @param userWordList
     */
    private void ocrAndMatch(ZcSubmitForm form, List<List<WordItem>> userWordList) {
        List<OCRResult> ocrList = new ArrayList<>();
        // 对图片进行ocr操作
        for (FilePaperImg img : form.getUserImgAnswerList()) {
            OCRResult result = ocrService.handWriting(img.getImgUrl());
            ocrList.add(result);
        }

        // 进行匹配
        for (int i = 0; i < Math.min(ocrList.size(), userWordList.size()); i++) {
            // 对应页数的ocr
            OCRResult result = ocrList.get(i);

            if (ObjectUtil.isNull(result) || CollUtil.isEmpty(result.getWords_result())) {
                continue;
            }

            // 获取全部的字符级别
            List<OCRChars> charList = result.getWords_result().stream()
                    .flatMap(word -> word.getChars().stream())
                    .toList();

            // 对应页数的坐标
            List<WordItem> userWordItem = userWordList.get(i);

            // 全部的字符
            for (OCRChars chars : charList) {

                Rectangle2D.Float charRect = new Rectangle2D.Float(
                        chars.getLocation().getLeft(), chars.getLocation().getTop(),
                        chars.getLocation().getWidth(), chars.getLocation().getHeight()
                );

                // 筛选符合的坐标数组
                List<WordItem> list = userWordItem.stream().filter(wordItem -> {
                    // 凡是坐标在范围内的
                    Rectangle2D.Float questionRect = new Rectangle2D.Float(
                            wordItem.getLocation().getLeft(),
                            wordItem.getLocation().getTop(),
                            wordItem.getLocation().getWidth() * 1.2f,
                            wordItem.getLocation().getHeight() * 1.2f);

                    // 判断是否有交集
                    return questionRect.intersects(charRect);
                }).toList();

                // 如果数组大于1，则判断中心位置最近的那一个
                // if (list.size() > 1) {
                //
                //     List<WordItem> sortList = new ArrayList<>(list);
                //
                //     sortList.sort((o1, o2) -> {
                //         // 计算中心
                //         double x1 = o1.getLocation().getLeft() + o1.getLocation().getWidth() / 2;
                //         double y1 = o1.getLocation().getTop() + o1.getLocation().getHeight() / 2;
                //         double x2 = o2.getLocation().getLeft() + o2.getLocation().getWidth() / 2;
                //         double y2 = o2.getLocation().getTop() + o2.getLocation().getHeight() / 2;
                //
                //         // 计算最近的那个
                //         double distance1 = Math.sqrt(Math.pow(chars.getLocation().getLeft() - x1, 2) + Math.pow(chars.getLocation().getTop() - y1, 2));
                //         double distance2 = Math.sqrt(Math.pow(chars.getLocation().getLeft() - x2, 2) + Math.pow(chars.getLocation().getTop() - y2, 2));
                //
                //         return (int) (distance1 - distance2);
                //     });
                // }

                if (CollUtil.isEmpty(list)) continue;

                // 赋值内容
                // WordItem wordItem = list.get(0);
                for (WordItem wordItem : list) {
                    int index = userWordItem.indexOf(wordItem);
                    userWordItem.get(index).setUserContent(
                            StrUtil.isBlank(wordItem.getUserContent()) ? chars.getChars() : wordItem.getUserContent() + chars.getChars()
                    );
                }

            }
        }

    }


    /**
     * 【拼音和词语】计算正误
     *
     * @param type
     * @param userWordList
     */
    private void calcCorrect(ZcQuestionTypeEnum type, List<List<WordItem>> userWordList) {

        for (List<WordItem> wordItemList : userWordList) {
            for (WordItem wordItem : wordItemList) {
                // 未匹配 跳过
                if (StrUtil.isBlank(wordItem.getUserContent())) {
//                    wordItem.setRightType(0);
                    continue;
                }

                // 【看拼音写词语】
                if (type.equals(ZcQuestionTypeEnum.PyToWord)) {

                    // 遍历每一个字符
                    for (ZcChar zcChar : wordItem.getCharList()) {

                        if (ObjectUtil.isNull(zcChar.getIsShow()) || zcChar.getIsShow().equals(false)) {

                            if (wordItem.getUserContent().contains(zcChar.getChars())) {
                                zcChar.setRightType(1);
                            } else {
                                zcChar.setRightType(2);
                            }
                        } else {
                            zcChar.setRightType(0);
                        }
                    }
                    // 如果item里的zcChar 的如果有2 就认为是2，如果全为1，则为1，否则就是0
                    if (wordItem.getCharList().stream().anyMatch(zcChar -> zcChar.getRightType().equals(2))) {

                        // 记录标注位置 - 错别字的位置
//                        List<MarkLoc> markLocs = wordItem.getCharList().stream()
//                                .filter(zcChar -> zcChar.getRightType().equals(2))
//                                .map(zcChar -> {
//                                    MarkLoc markLoc = new MarkLoc();
//                                    markLoc.setZcLocation(zcChar.getLocation());
//                                    markLoc.setErrorWord(zcChar.getChars());
//                                    markLoc.setRightType(2);
//                                    return markLoc;
//                                })
//                                .toList();
                        List<MarkLoc> markLocs = new ArrayList<>();
                        MarkLoc markLoc = new MarkLoc();
                        markLoc.setZcLocation(wordItem.getLocation());
                        markLoc.setRightType(2);
                        markLoc.setErrorWord(wordItem.getWord());
                        markLocs.add(markLoc);

                        wordItem.setMarkLocation(markLocs);
//                        wordItem.setRightType(2);
                    }
                    // 全部正确 -- 标注整体位置
                    else if (wordItem.getCharList().stream().allMatch(zcChar -> zcChar.getRightType().equals(1))) {

                        List<MarkLoc> markLocs = new ArrayList<>();
                        MarkLoc markLoc = new MarkLoc();
                        markLoc.setZcLocation(wordItem.getLocation());
                        markLoc.setRightType(1);
                        markLocs.add(markLoc);
                        wordItem.setMarkLocation(markLocs);
//                        wordItem.setRightType(1);
                    }
                    // 不标注
                    else {
//                        wordItem.setRightType(0);
                    }
                }
                // 【看词语写拼音】
                else if (type.equals(ZcQuestionTypeEnum.WordToPy)) {

                    // 遍历每一个拼音
                    for (ZcChar zcChar : wordItem.getPinyinList()) {
                        if (ObjectUtil.isNull(zcChar.getIsShow()) || zcChar.getIsShow().equals(false)) {

                            if (wordItem.getUserContent().contains(zcChar.getChars())) {
                                zcChar.setRightType(1);
                            } else {
                                zcChar.setRightType(2);
                            }
                        } else {
                            zcChar.setRightType(0);
                        }
                    }
                    // 设置标注位置
                    if (wordItem.getPinyinList().stream().anyMatch(zcChar -> zcChar.getRightType().equals(2))) {
                        // 收集错误拼音位置
//                        List<MarkLoc> markLocs = wordItem.getPinyinList().stream()
//                                .filter(zcChar -> zcChar.getRightType().equals(2))
//                                .map(zcChar -> {
//                                    MarkLoc markLoc = new MarkLoc();
//                                    markLoc.setZcLocation(zcChar.getLocation());
//                                    markLoc.setErrorWord(zcChar.getChars());
//                                    markLoc.setRightType(2);
//                                    return markLoc;
//                                })
//                                .toList();
                        List<MarkLoc> markLocs = new ArrayList<>();
                        MarkLoc markLoc = new MarkLoc();
                        markLoc.setZcLocation(wordItem.getLocation());
                        markLoc.setRightType(2);
                        markLocs.add(markLoc);
                        wordItem.setMarkLocation(markLocs);
//                        wordItem.setRightType(2);
                    }
                    // 全部正确
                    else if (wordItem.getPinyinList().stream().allMatch(zcChar -> zcChar.getRightType().equals(1))) {
                        List<MarkLoc> markLocs = new ArrayList<>();
                        MarkLoc markLoc = new MarkLoc();
                        markLoc.setZcLocation(wordItem.getLocation());
                        markLoc.setRightType(1);
                        markLocs.add(markLoc);
                        wordItem.setMarkLocation(markLocs);
//                        wordItem.setRightType(1);
                    }
                }
            }
        }
    }


    /**
     * 计算坐标区域
     *
     * @param forms
     * @return
     */
    private List<CalcAreaResult> calculateArea(List<CalcAreaForm> forms) {

        // 发送请求
//        String baseUrl = "http://47.98.182.152:9100/region_detector";
//        String baseUrl = "http://test-api-2.pigaibang.com/region_detector";
//        String baseUrl = "http://192.168.100.110:3001/region_detector";

        String baseUrl = EnvUtils.isDev() ? "https://cv-region-izgznzztgx.cn-beijing.fcapp.run/region_detector" : "https://cv-region-izgznzztgx.cn-beijing-vpc.fcapp.run/region_detector";

        // 执行渲染
        HttpResponse response = HttpRequest.post(baseUrl)
                .body(JSONUtil.toJsonStr(forms))
                .timeout(5 * 60 * 1000)
                .execute();

        if (response.isOk()) {

            return JSONUtil.toList(response.body(), CalcAreaResult.class);

        } else {
            log.error("区域计算失败，错误信息：{}", response.body());
            throw new RuntimeException("区域计算失败");
        }
    }

    /**
     * 替换用户图片
     *
     * @param userImgBuffer
     * @param userImgUrl
     * @param userId
     * @return
     */
    private String replaceUserImg(BufferedImage userImgBuffer, String userImgUrl, Long userId) {

        // 取后缀
        String suffix = FileUtil.extName(userImgUrl);

        // 替换用户上传的图片
        String path = StrUtil.format(
                "zc/user/{}/IMG/{}.{}",
                userId,
                DigestUtil.md5Hex(ImgUtil.toBytes(userImgBuffer, suffix)),
                suffix
        );
        String newImgUrl = ossService.putCdnImg(path, userImgBuffer);

        return newImgUrl;

    }

    /**
     * 清洗文本，去除标点符号和特殊字符，只保留中文字符
     *
     * @param text 原始文本
     * @return 清洗后的文本
     */
    private String cleanText(String text) {
        if (StrUtil.isBlank(text)) {
            return "";
        }

        // 使用正则表达式只保留中文字符
        return text.replaceAll("[^\\u4E00-\\u9FA5]", "");
    }

    /**
     * 创建原始文本到清洗后文本的位置映射
     * 处理跨行书写时OCR坐标顺序可能不正确的问题
     *
     * @param originalText 原始文本
     * @param cleanedText 清洗后的文本
     * @return 位置映射列表，索引为清洗后文本的位置，值为原始文本的位置
     */
    private List<Integer> createPositionMapping(String originalText, String cleanedText) {
        List<Integer> mapping = new ArrayList<>();

        if (StrUtil.isBlank(originalText) || StrUtil.isBlank(cleanedText)) {
            return mapping;
        }

        char[] originalChars = originalText.toCharArray();
        char[] cleanedChars = cleanedText.toCharArray();

        int cleanedIndex = 0;

        // 遍历原始文本，找到每个中文字符在原始文本中的位置
        for (int originalIndex = 0; originalIndex < originalChars.length && cleanedIndex < cleanedChars.length; originalIndex++) {
            char originalChar = originalChars[originalIndex];

            // 如果是中文字符
            if (String.valueOf(originalChar).matches("[\\u4E00-\\u9FA5]")) {
                // 检查是否与清洗后文本的当前字符匹配
                if (originalChar == cleanedChars[cleanedIndex]) {
                    mapping.add(originalIndex);
                    cleanedIndex++;
                }
            }
        }

        return mapping;
    }

    /**
     * 修正用户文字坐标列表，处理跨行书写导致的坐标顺序问题
     *
     * @param userTextLocationList 原始用户文字坐标列表
     * @param cleanedUserAnswer 清洗后的用户答案
     * @param standardAnswer 标准答案
     * @return 修正后的坐标列表
     */
    private List<ZcLocation> fixUserTextLocationList(List<ZcLocation> userTextLocationList,
                                                     String cleanedUserAnswer,
                                                     String standardAnswer) {

        // 如果用户答案长度与坐标数量不匹配，可能存在跨行问题
        if (userTextLocationList.size() != cleanedUserAnswer.length()) {
            log.warn("用户答案长度({})与坐标数量({})不匹配，可能存在跨行书写问题",
                    cleanedUserAnswer.length(), userTextLocationList.size());

            // 如果坐标数量大于用户答案长度，尝试取后面的坐标（通常跨行时前面的坐标是错误的）
            if (userTextLocationList.size() > cleanedUserAnswer.length()) {
                int startIndex = userTextLocationList.size() - cleanedUserAnswer.length();
                return userTextLocationList.subList(startIndex, userTextLocationList.size());
            }
        }

        return userTextLocationList;
    }


}
